"""
Response database and pattern matching for the AI Chatbot
"""

import re
import random
from config import BOT_NAME, COMPANY_NAME, BUSINESS_HOURS, CONTACT_EMAIL, CONTACT_PHONE, USE_EMOJIS

class ResponseDatabase:
    def __init__(self):
        self.patterns = {
            # Greetings and basic conversation
            'greeting': {
                'patterns': [
                    r'\b(hi|hello|hey|good morning|good afternoon|good evening)\b',
                    r'\bhow are you\b',
                    r'\bwhat\'s up\b'
                ],
                'responses': [
                    f"Hello! 👋 I'm {BOT_NAME}, your friendly AI assistant from {COMPANY_NAME}. How can I help you today?",
                    f"Hi there! 😊 Welcome to {COMPANY_NAME}! I'm here to assist you. What can I do for you?",
                    f"Hey! Great to see you! I'm {BOT_NAME}, and I'm excited to help you with anything you need. What's on your mind?"
                ]
            },
            
            # Product/Service inquiries
            'services': {
                'patterns': [
                    r'\b(services|products|what do you offer|solutions)\b',
                    r'\bwhat can you do\b',
                    r'\btell me about\b'
                ],
                'responses': [
                    f"🚀 {COMPANY_NAME} offers cutting-edge technology solutions including:\n• Custom software development\n• AI & Machine Learning solutions\n• Cloud services\n• Digital transformation consulting\n\nWhich area interests you most?",
                    f"We specialize in innovative tech solutions! 💡 Our main services include software development, AI implementation, and digital consulting. Would you like details about any specific service?",
                    f"Great question! We're experts in:\n✅ Custom applications\n✅ AI chatbots (like me!)\n✅ Cloud migration\n✅ Tech consulting\n\nWhat project are you working on?"
                ]
            },
            
            # Pricing inquiries
            'pricing': {
                'patterns': [
                    r'\b(price|cost|pricing|how much|budget|quote)\b',
                    r'\baffordable\b',
                    r'\bexpensive\b'
                ],
                'responses': [
                    f"💰 Our pricing is competitive and tailored to your needs! Each project is unique, so I'd love to understand your requirements better. Can you tell me more about what you're looking for?",
                    f"Great question about pricing! 📊 We offer flexible packages starting from basic solutions to enterprise-level implementations. Would you like me to connect you with our sales team for a personalized quote?",
                    f"We believe in transparent, value-based pricing! 💎 Costs vary depending on project scope and complexity. Let me gather some details to provide you with accurate information. What's your project about?"
                ]
            },
            
            # Contact and support
            'contact': {
                'patterns': [
                    r'\b(contact|phone|email|address|location)\b',
                    r'\bhow to reach\b',
                    r'\bget in touch\b'
                ],
                'responses': [
                    f"📞 You can reach us at:\n• Phone: {CONTACT_PHONE}\n• Email: {CONTACT_EMAIL}\n• Business Hours: {BUSINESS_HOURS}\n\nI'm also here 24/7 to help! What would you like to know?",
                    f"Here's how to connect with us! 📧\n{CONTACT_EMAIL} | {CONTACT_PHONE}\n\nOur team is available during {BUSINESS_HOURS}. For immediate assistance, I'm your go-to bot! How can I help?",
                    f"Let's get you connected! 🤝\n• Email us: {CONTACT_EMAIL}\n• Call us: {CONTACT_PHONE}\n• Chat with me anytime!\n\nWhat's the best way to assist you right now?"
                ]
            },
            
            # Business hours
            'hours': {
                'patterns': [
                    r'\b(hours|open|closed|when|schedule|time)\b',
                    r'\bwhat time\b',
                    r'\bavailable\b'
                ],
                'responses': [
                    f"🕒 Our business hours are: {BUSINESS_HOURS}\n\nBut here's the good news - I'm available 24/7 to help you! What can I assist you with right now?",
                    f"We're open {BUSINESS_HOURS} ⏰\n\nThough our human team has set hours, I'm always here to help! What questions do you have?",
                    f"Our office hours: {BUSINESS_HOURS} 🏢\n\nI never sleep though! 😄 Feel free to ask me anything anytime. How can I help you today?"
                ]
            },
            
            # Technical support
            'support': {
                'patterns': [
                    r'\b(help|support|problem|issue|bug|error)\b',
                    r'\bnot working\b',
                    r'\btrouble\b'
                ],
                'responses': [
                    f"🛠️ I'm here to help! Can you describe the issue you're experiencing? The more details you provide, the better I can assist you.",
                    f"No worries, let's get this sorted out! 💪 What specific problem are you facing? I'll do my best to help or connect you with our technical team.",
                    f"Support is what we do best! 🎯 Please tell me:\n• What were you trying to do?\n• What happened instead?\n• Any error messages?\n\nI'm here to help!"
                ]
            },
            
            # Lead generation
            'interested': {
                'patterns': [
                    r'\b(interested|want to know more|tell me more|sign up)\b',
                    r'\bget started\b',
                    r'\bhow to begin\b'
                ],
                'responses': [
                    f"That's fantastic! 🎉 I'd love to learn more about your needs. Could you share:\n• Your name\n• Your email\n• What type of solution you're looking for?\n\nThis helps us provide the best assistance!",
                    f"Excellent! Let's get you started! 🚀 To provide personalized recommendations, I'll need:\n• Your name\n• Contact email\n• Brief description of your project\n\nWhat's your name?",
                    f"Perfect timing! 💫 We'd love to help you succeed. Can you tell me your name and email so our team can follow up with tailored solutions?"
                ]
            },
            
            # Goodbye
            'goodbye': {
                'patterns': [
                    r'\b(bye|goodbye|see you|thanks|thank you|that\'s all)\b',
                    r'\bhave a good\b',
                    r'\btalk later\b'
                ],
                'responses': [
                    f"Thank you for chatting with me! 😊 It was great helping you today. Feel free to reach out anytime - I'm always here! Have a wonderful day! 🌟",
                    f"Goodbye! 👋 Thanks for visiting {COMPANY_NAME}. Remember, I'm available 24/7 whenever you need assistance. Take care! 💙",
                    f"It was a pleasure talking with you! 🤗 Don't hesitate to come back if you have more questions. Wishing you success with your projects! ✨"
                ]
            },
            
            # Default/fallback responses
            'default': {
                'patterns': [],
                'responses': [
                    f"That's an interesting question! 🤔 While I might not have the perfect answer right now, I'd love to help you find what you're looking for. Could you rephrase or tell me more?",
                    f"I want to make sure I understand you correctly! 💭 Could you provide a bit more detail about what you're looking for? I'm here to help!",
                    f"Great question! 🌟 I'm still learning, but I'm eager to assist. Can you tell me more about what you need help with? Or would you like to speak with our human team?"
                ]
            }
        }
    
    def find_response(self, user_input):
        """Find the best response for user input"""
        user_input = user_input.lower().strip()
        
        # Check each pattern category
        for category, data in self.patterns.items():
            if category == 'default':
                continue
                
            for pattern in data['patterns']:
                if re.search(pattern, user_input, re.IGNORECASE):
                    return random.choice(data['responses'])
        
        # Return default response if no pattern matches
        return random.choice(self.patterns['default']['responses'])
    
    def get_escalation_response(self):
        """Response for escalating to human support"""
        return f"I understand you'd like to speak with a human representative! 👥 Let me connect you with our team. Please provide your name and email, and someone will reach out to you within 2 hours during business hours ({BUSINESS_HOURS})."
