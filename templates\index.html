<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chatbot - TechSolutions Pro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.bot {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.bot .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
        }

        .message.user .message-avatar {
            background: #667eea;
            order: 2;
        }

        .message.bot .message-avatar {
            background: #4facfe;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .chat-input-form {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #4facfe;
        }

        .send-button {
            padding: 12px 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 10px 16px;
            color: #666;
            font-style: italic;
        }

        .reset-button {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .reset-button:hover {
            background: rgba(255,255,255,0.3);
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 10px;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <button class="reset-button" onclick="resetChat()">🔄 Reset</button>
            <div class="status-indicator"></div>
            <h1>🤖 AI Assistant</h1>
            <p>Powered by TechSolutions Pro</p>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <!-- Messages will be added here dynamically -->
        </div>
        
        <div class="typing-indicator" id="typingIndicator">
            Alex is typing...
        </div>
        
        <div class="chat-input-container">
            <form class="chat-input-form" onsubmit="sendMessage(event)">
                <input 
                    type="text" 
                    class="chat-input" 
                    id="messageInput" 
                    placeholder="Type your message here..." 
                    autocomplete="off"
                    maxlength="500"
                >
                <button type="submit" class="send-button" id="sendButton">
                    Send 🚀
                </button>
            </form>
        </div>
    </div>

    <script>
        let isWaitingForResponse = false;

        // Initialize chat when page loads
        window.onload = function() {
            loadWelcomeMessage();
        };

        async function loadWelcomeMessage() {
            try {
                const response = await fetch('/api/welcome');
                const data = await response.json();
                
                if (data.message) {
                    addMessage(data.message, 'bot');
                }
            } catch (error) {
                console.error('Error loading welcome message:', error);
                addMessage('Hello! I\'m having trouble connecting right now, but I\'m here to help! 😊', 'bot');
            }
        }

        async function sendMessage(event) {
            event.preventDefault();
            
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message || isWaitingForResponse) return;
            
            // Add user message to chat
            addMessage(message, 'user');
            messageInput.value = '';
            
            // Show typing indicator
            showTypingIndicator();
            
            // Disable input while waiting for response
            setInputState(false);
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                const data = await response.json();
                
                if (data.response) {
                    // Simulate typing delay for better UX
                    setTimeout(() => {
                        hideTypingIndicator();
                        addMessage(data.response, 'bot');
                        setInputState(true);
                    }, 1000);
                } else {
                    throw new Error(data.error || 'Unknown error');
                }
                
            } catch (error) {
                console.error('Error sending message:', error);
                hideTypingIndicator();
                addMessage('Sorry, I\'m having trouble responding right now. Please try again! 🔧', 'bot');
                setInputState(true);
            }
        }

        function addMessage(message, sender) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = sender === 'user' ? 'You' : '🤖';
            
            const content = document.createElement('div');
            content.className = 'message-content';
            content.textContent = message;
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(content);
            chatMessages.appendChild(messageDiv);
            
            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'block';
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        function setInputState(enabled) {
            isWaitingForResponse = !enabled;
            document.getElementById('messageInput').disabled = !enabled;
            document.getElementById('sendButton').disabled = !enabled;
            
            if (enabled) {
                document.getElementById('messageInput').focus();
            }
        }

        async function resetChat() {
            if (confirm('Are you sure you want to reset the chat? This will clear all messages.')) {
                try {
                    await fetch('/api/reset', { method: 'POST' });
                    
                    // Clear chat messages
                    document.getElementById('chatMessages').innerHTML = '';
                    
                    // Load welcome message again
                    loadWelcomeMessage();
                    
                } catch (error) {
                    console.error('Error resetting chat:', error);
                    alert('Error resetting chat. Please refresh the page.');
                }
            }
        }

        // Allow Enter key to send message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage(e);
            }
        });
    </script>
</body>
</html>
